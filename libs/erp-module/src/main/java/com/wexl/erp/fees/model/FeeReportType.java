package com.wexl.erp.fees.model;

/**
 * Enum representing different types of fee reports with their specific configurations
 */
public enum FeeReportType {
  /**
   * Student Term-wise Report - Single-row header
   * Shows due amounts per student for each fee_head in term-wise format
   */
  STUDENT_TERM_WISE("student_term_wise", 1, "Student Term-wise Fee Report"),

  /**
   * Total Due Report - Two-row header structure
   * Filter condition: fee_heads.status IN (0,1,3) - UNPAID, PAID, PARTIALLY_PAID
   */
  TOTAL_DUE("total_due", 2, "Total Due Fee Report"),

  /**
   * Past Due Report - Two-row header structure
   * Filter condition: fee_heads.status IN (0,2) - UNPAID, OVERDUE
   */
  PAST_DUE("past_due", 2, "Past Due Fee Report"),

  /**
   * Fee Head Master Report - Three-row header structure
   * Most complex report with Fee Applicable, Fee Collected, Total Paid, Total Due sections
   */
  FEE_HEAD_MASTER("fee_head_master", 3, "Fee Head Master Report");

  private final String code;
  private final int headerRows;
  private final String displayName;

  FeeReportType(String code, int headerRows, String displayName) {
    this.code = code;
    this.headerRows = headerRows;
    this.displayName = displayName;
  }

  public String getCode() {
    return code;
  }

  public int getHeaderRows() {
    return headerRows;
  }

  public String getDisplayName() {
    return displayName;
  }

  /**
   * Get FeeReportType from string code
   */
  public static FeeReportType fromCode(String code) {
    if (code == null) {
      return TOTAL_DUE; // Default
    }

    for (FeeReportType type : values()) {
      if (type.code.equalsIgnoreCase(code)) {
        return type;
      }
    }

    return TOTAL_DUE; // Default fallback
  }

  /**
   * Get the fee status filter for this report type
   */
  public int[] getStatusFilter() {
    return switch (this) {
      case PAST_DUE -> new int[]{0, 2}; // UNPAID, OVERDUE
      case TOTAL_DUE, STUDENT_TERM_WISE, FEE_HEAD_MASTER -> new int[]{0, 1, 3}; // UNPAID, PAID, PARTIALLY_PAID
    };
  }

  /**
   * Check if this report type requires payment details
   */
  public boolean requiresPaymentDetails() {
    return this == FEE_HEAD_MASTER;
  }

  /**
   * Check if this report type groups by fee groups
   */
  public boolean groupsByFeeGroups() {
    return this == TOTAL_DUE || this == PAST_DUE || this == FEE_HEAD_MASTER;
  }
}