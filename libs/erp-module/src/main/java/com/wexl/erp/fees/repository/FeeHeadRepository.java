package com.wexl.erp.fees.repository;

import com.wexl.erp.fees.dto.DueDetails;
import com.wexl.erp.fees.model.*;
import com.wexl.retail.model.Student;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface FeeHeadRepository extends JpaRepository<FeeHead, Long> {

  boolean existsByFeeMasterAndStudentAndFeeType(
      FeeMaster feeMaster, Student student, FeeType feeType);

  List<FeeHead> findAllByFeeMasterAndOrgSlug(FeeMaster feeMaster, String orgSlug);

  List<FeeHead> findAllByStudentIdAndOrgSlugAndStatusNotIn(
      Long studentId, String orgSlug, List<FeeStatus> status);

  Optional<FeeHead> findByIdAndOrgSlug(UUID uuid, String orgSlug);

  List<FeeHead> findAllByConcession(Concession concession);

  Long countByFeeTypeIn(List<FeeType> feeTypes);

  Optional<FeeHead> findByIdAndOrgSlugAndStudent(UUID id, String orgSlug, Student student);

  @Query(
      value =
          """
          SELECT fh.*
          FROM fee_heads fh
                     JOIN fee_masters fm ON fm.id = fh.fee_master_id
                     JOIN fee_groups fg ON fg.id = fm.fee_group_id
           WHERE fh.org_slug = :orgSlug
             AND fh.student_id IN (:studentIds)
             AND fh.status IN (0, 2)
             AND fg.is_active = TRUE
             AND fg.name IN (:feeNames)
             AND fh.due_date >= :fromDate
             AND fh.due_date <= :toDate
           ORDER BY fh.student_id, fh.due_date""",
      nativeQuery = true)
  List<FeeHead> findPastDueFeeDetails(
      @Param("orgSlug") String orgSlug,
      @Param("studentIds") List<Long> studentIds,
      @Param("feeNames") List<String> feeNames,
      @Param("fromDate") LocalDateTime fromDate,
      @Param("toDate") LocalDateTime toDate);

  @Query(
      value =
          """
          SELECT fh.*
          FROM fee_heads fh
                    JOIN fee_masters fm ON fm.id = fh.fee_master_id
                    JOIN fee_groups fg ON fg.id = fm.fee_group_id
          WHERE fh.org_slug = :orgSlug
            AND fh.student_id IN (:studentIds)
            AND fh.status IN (0,1,3)
            AND fg.is_active = TRUE
            AND fg.name IN (:feeNames)
            AND fh.due_date >= :fromDate
            AND fh.due_date <= :toDate
          ORDER BY fh.student_id, fh.due_date""",
      nativeQuery = true)
  List<FeeHead> findTotalDueFeeDetails(
      @Param("orgSlug") String orgSlug,
      @Param("studentIds") List<Long> studentIds,
      @Param("feeNames") List<String> feeNames,
      @Param("fromDate") LocalDateTime fromDate,
      @Param("toDate") LocalDateTime toDate);

  List<FeeHead> findAllByOrgSlug(String orgSlug);

  Optional<FeeHead> findByStudentAndFeeType(Student studentInfo, FeeType feeType);

  List<FeeHead> findAllByStudent(Student student);

  @Query(
      value =
          """
                  select fh.id as id,DATE(fh.due_date) as due_date,fh.balance_amount as due,fh.amount as total_fee,fh.fine_amount as fine,ft."name" as type_name from fee_heads fh
                  join fee_types ft on fh.fee_type_id = ft.id
                  where fh.org_slug = :orgSlug and student_id = :studentID and fh.due_date::date <= DATE(:date) and ft."name" != 'Admission Fee'
          """,
      nativeQuery = true)
  List<DueDetails> getByOrgSlugAndStudentIdAndDueDate(
      String orgSlug, Long studentID, LocalDate date);

  @Query(
      value =
          """

                                  select fh.* from fee_heads fh
                                  join fee_masters fm  on fm.id=fh.fee_master_id
                                  where fm.fee_group_id  = :feeGroupId and fh.org_slug = :orgSlug
                                  and fh.student_id = :studentId and fh.fee_type_id = :feeTypeId
                  """,
      nativeQuery = true)
  Optional<FeeHead> getFeeHeadByStudentIdAnFeeMasterIdAndFeeType(
      UUID feeGroupId, String orgSlug, Long studentId, UUID feeTypeId);
}
